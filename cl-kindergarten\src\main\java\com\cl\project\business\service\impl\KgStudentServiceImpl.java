package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.service.IKgStudentService;
import com.cl.project.business.service.IKgClassService;

/**
 * 幼儿信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgStudentServiceImpl implements IKgStudentService
{
    @Autowired
    private KgStudentMapper kgStudentMapper;

    @Autowired
    private IKgClassService kgClassService;

    /**
     * 查询幼儿信息
     * 
     * @param studentId 幼儿信息ID
     * @return 幼儿信息
     */
    @Override
    public KgStudent selectKgStudentById(Long studentId)
    {
        return kgStudentMapper.selectKgStudentById(studentId);
    }

    /**
     * 查询幼儿信息列表
     * 
     * @param kgStudent 幼儿信息
     * @return 幼儿信息
     */
    @Override
    public List<KgStudent> selectKgStudentList(KgStudent kgStudent)
    {
        return kgStudentMapper.selectKgStudentList(kgStudent);
    }

    /**
     * 新增幼儿信息
     *
     * @param kgStudent 幼儿信息
     * @return 结果
     */
    @Override
    public int insertKgStudent(KgStudent kgStudent)
    {
        // 生成唯一学生编码: STU + 日期(yyyyMMdd) + 4位随机数
        String dateStr = DateUtils.formatDate(DateUtils.getNowDate(), "yyyyMMdd");
        String randomNum = String.format("%04d", new java.util.Random().nextInt(10000));
        String studentCode = "STU" + dateStr + randomNum;
        kgStudent.setStudentCode(studentCode);

        kgStudent.setCreateTime(DateUtils.getNowDate());
        int result = kgStudentMapper.insertKgStudent(kgStudent);

        // 更新班级当前人数
        if (result > 0 && kgStudent.getClassId() != null) {
            try {
                kgClassService.updateClassCurrentCount(kgStudent.getClassId());
            } catch (Exception e) {
                // 记录日志但不影响主流程
                System.err.println("更新班级人数失败: " + e.getMessage());
            }
        }

        return result;
    }

    /**
     * 修改幼儿信息
     *
     * @param kgStudent 幼儿信息
     * @return 结果
     */
    @Override
    public int updateKgStudent(KgStudent kgStudent)
    {
        // 获取原始学生信息，用于比较班级是否变更
        KgStudent originalStudent = null;
        if (kgStudent.getStudentId() != null) {
            originalStudent = kgStudentMapper.selectKgStudentById(kgStudent.getStudentId());
        }

        kgStudent.setUpdateTime(DateUtils.getNowDate());
        int result = kgStudentMapper.updateKgStudent(kgStudent);

        // 更新相关班级的当前人数
        if (result > 0) {
            try {
                // 如果班级发生变更，需要更新两个班级的人数
                if (originalStudent != null && originalStudent.getClassId() != null) {
                    if (kgStudent.getClassId() == null || !originalStudent.getClassId().equals(kgStudent.getClassId())) {
                        // 原班级人数减少
                        kgClassService.updateClassCurrentCount(originalStudent.getClassId());
                    }
                }

                // 更新新班级人数
                if (kgStudent.getClassId() != null) {
                    kgClassService.updateClassCurrentCount(kgStudent.getClassId());
                }
            } catch (Exception e) {
                // 记录日志但不影响主流程
                System.err.println("更新班级人数失败: " + e.getMessage());
            }
        }

        return result;
    }

    /**
     * 批量删除幼儿信息
     *
     * @param studentIds 需要删除的幼儿信息ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentByIds(Long[] studentIds)
    {
        // 获取要删除的学生信息，用于更新班级人数
        java.util.Set<Long> classIds = new java.util.HashSet<>();
        for (Long studentId : studentIds) {
            KgStudent student = kgStudentMapper.selectKgStudentById(studentId);
            if (student != null && student.getClassId() != null) {
                classIds.add(student.getClassId());
            }
        }

        int result = kgStudentMapper.deleteKgStudentByIds(studentIds);

        // 更新相关班级的当前人数
        if (result > 0) {
            try {
                for (Long classId : classIds) {
                    kgClassService.updateClassCurrentCount(classId);
                }
            } catch (Exception e) {
                // 记录日志但不影响主流程
                System.err.println("更新班级人数失败: " + e.getMessage());
            }
        }

        return result;
    }

    /**
     * 删除幼儿信息信息
     *
     * @param studentId 幼儿信息ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentById(Long studentId)
    {
        // 获取要删除的学生信息，用于更新班级人数
        KgStudent student = kgStudentMapper.selectKgStudentById(studentId);
        Long classId = (student != null) ? student.getClassId() : null;

        int result = kgStudentMapper.deleteKgStudentById(studentId);

        // 更新班级当前人数
        if (result > 0 && classId != null) {
            try {
                kgClassService.updateClassCurrentCount(classId);
            } catch (Exception e) {
                // 记录日志但不影响主流程
                System.err.println("更新班级人数失败: " + e.getMessage());
            }
        }

        return result;
    }
    
    /**
     * 根据钉钉用户ID查询学生信息
     * 
     * @param dingtalkUserId 钉钉用户ID
     * @return 学生信息
     */
    @Override
    public KgStudent selectKgStudentByDingtalkUserId(String dingtalkUserId)
    {
        return kgStudentMapper.selectKgStudentByDingtalkUserId(dingtalkUserId);
    }
}
