<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="统计月份" prop="statMonth">
        <el-date-picker
          v-model="queryDate"
          type="month"
          placeholder="选择年月"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="教师" prop="teacherId">
        <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable filterable>
          <el-option
            v-for="teacher in teacherOptions"
            :key="teacher.teacherId"
            :label="teacher.teacherName"
            :value="teacher.teacherId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程" prop="courseId">
        <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable>
          <el-option
            v-for="course in courseOptions"
            :key="course.courseId"
            :label="course.courseName"
            :value="course.courseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Operation"
          @click="handleCalculate"
          :loading="calculateLoading"
        >计算课时费</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出统计</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon teacher">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="stat-value">{{ summary.totalTeachers }}</div>
            <div class="stat-label">授课教师数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon sessions">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-value">{{ summary.totalSessions }}</div>
            <div class="stat-label">总授课节数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon fee">
              <i class="el-icon-money"></i>
            </div>
            <div class="stat-value">{{ summary.totalCourseFee }}</div>
            <div class="stat-label">总课时费</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon average">
              <i class="el-icon-coin"></i>
            </div>
            <div class="stat-value">{{ summary.avgFeePerSession }}</div>
            <div class="stat-label">平均单节费用</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="teacherStatsList" show-summary :summary-method="getSummaries">
      <el-table-column label="教师姓名" prop="teacherName" width="120" fixed="left" />
      <el-table-column label="教师编号" prop="teacherCode" width="120" />
      <el-table-column label="课程明细" min-width="300">
        <template #default="scope">
          <div class="course-details">
            <div v-for="course in scope.row.courseDetails" :key="course.courseId" class="course-detail-item">
              <el-tag size="small" class="course-tag">{{ course.courseName }}</el-tag>
              <span class="course-info">
                {{ course.sessions }}节 × ￥{{ course.pricePerSession }} = 
                <span class="course-subtotal">￥{{ course.subtotal }}</span>
              </span>
            </div>
            <div v-if="!scope.row.courseDetails || scope.row.courseDetails.length === 0" class="no-data">
              暂无授课记录
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="总授课节数" align="center" prop="totalSessions" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.totalSessions > 0 ? 'success' : 'info'">
            {{ scope.row.totalSessions }}节
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总课时费" align="center" width="120">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.totalCourseFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="平均单节费用" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.totalSessions > 0">
            ￥{{ (scope.row.totalCourseFee / scope.row.totalSessions).toFixed(2) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="最后更新" align="center" prop="updateTime" width="140" />
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewDetail(scope.row)">详细记录</el-button>
          <el-button link type="success" @click="handleRecalculate(scope.row)">重新计算</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详细记录对话框 -->
    <el-dialog :title="`${currentTeacher.teacherName} - 详细授课记录`" v-model="detailDialogVisible" width="1000px">
      <div v-if="attendanceDetails.length > 0">
        <el-table :data="attendanceDetails" border max-height="400">
          <el-table-column label="日期" prop="attendanceDate" width="100" />
          <el-table-column label="课程名称" prop="courseName" width="120" />
          <el-table-column label="课程类型" width="100">
            <template #default="scope">
              <dict-tag :options="dict.type.kg_course_type" :value="scope.row.courseType"/>
            </template>
          </el-table-column>
          <el-table-column label="上课时间" width="180">
            <template #default="scope">
              <span>{{ scope.row.startTime }} - {{ scope.row.endTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学生数量" align="center" width="100">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.studentCount }}人</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="单节费用" align="center" width="100">
            <template #default="scope">
              <span>￥{{ scope.row.pricePerSession }}</span>
            </template>
          </el-table-column>
          <el-table-column label="确认状态" align="center" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isConfirmed ? 'success' : 'warning'">
                {{ scope.row.isConfirmed ? '已确认' : '待确认' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="150" />
        </el-table>
      </div>
      <el-empty v-else description="暂无授课记录" />
    </el-dialog>

    <!-- 计算课时费对话框 -->
    <el-dialog title="计算课时费" v-model="calculateDialogVisible" width="500px">
      <el-form ref="calculateRef" :model="calculateForm" label-width="100px">
        <el-form-item label="计算月份" required>
          <el-date-picker
            v-model="calculateForm.calculateMonth"
            type="month"
            placeholder="选择年月"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item label="教师范围">
          <el-radio-group v-model="calculateForm.calculateType">
            <el-radio label="all">所有教师</el-radio>
            <el-radio label="single">指定教师</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="calculateForm.calculateType === 'single'" label="选择教师">
          <el-select v-model="calculateForm.teacherId" placeholder="请选择教师" filterable>
            <el-option
              v-for="teacher in teacherOptions"
              :key="teacher.teacherId"
              :label="teacher.teacherName"
              :value="teacher.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="只计算已确认">
          <el-switch v-model="calculateForm.confirmedOnly" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmCalculate" :loading="calculateLoading">开始计算</el-button>
          <el-button @click="calculateDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="TeacherCourseStats">
import { 
  listTeacherStats as listTeacherCourseStats,
  calculateTeacherStats as calculateTeacherCourseFee,
  getTeacherCourseDetails as getTeacherAttendanceDetails,
  getTeacherStatsSummary as getTeacherCourseSummary
} from "@/api/kg/course/teacher-stats";
import { listAllTeacher } from "@/api/kg/teacher/info";
import { listAllCourse } from "@/api/kg/course/manage";
import dict from '@/mixin/dict'

export default {
  name: "TeacherCourseStats",
  dicts: ['kg_course_type'],
  mixins: [dict],
  data() {
    return {
      teacherStatsList: [],
      teacherOptions: [],
      courseOptions: [],
      attendanceDetails: [],
      loading: true,
      showSearch: true,
      total: 0,
      queryDate: '',
      detailDialogVisible: false,
      calculateDialogVisible: false,
      calculateLoading: false,
      currentTeacher: {},
      // 统计汇总数据
      summary: {
        totalTeachers: 0,
        totalSessions: 0,
        totalCourseFee: 0,
        avgFeePerSession: 0
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        statYear: null,
        statMonth: null,
        teacherId: null,
        courseId: null
      },
      calculateForm: {
        calculateMonth: '',
        calculateType: 'all',
        teacherId: null,
        confirmedOnly: true
      }
    };
  },
  methods: {
    /** 查询统计列表 */
    getList() {
      this.loading = true;
      listTeacherCourseStats(this.queryParams).then(response => {
        this.teacherStatsList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
      
      // 获取汇总数据
      getTeacherCourseSummary(this.queryParams).then(response => {
        this.summary = response.data || {
          totalTeachers: 0,
          totalSessions: 0,
          totalCourseFee: 0,
          avgFeePerSession: 0
        };
      }).catch(() => {
        // 如果获取汇总数据失败，保持默认值
        console.warn('Failed to load teacher course summary');
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryRef && this.$refs.queryRef.resetFields();
      this.queryDate = '';
      this.queryParams.statYear = null;
      this.queryParams.statMonth = null;
      this.handleQuery();
    },
    /** 日期变化处理 */
    handleDateChange(value) {
      if (value) {
        const [year, month] = value.split('-');
        this.queryParams.statYear = parseInt(year);
        this.queryParams.statMonth = parseInt(month);
      } else {
        this.queryParams.statYear = null;
        this.queryParams.statMonth = null;
      }
    },
    /** 计算课时费 */
    handleCalculate() {
      this.calculateForm = {
        calculateMonth: this.queryDate || new Date().toISOString().slice(0, 7),
        calculateType: 'all',
        teacherId: null,
        confirmedOnly: true
      };
      this.calculateDialogVisible = true;
    },
    /** 确认计算 */
    confirmCalculate() {
      if (!this.calculateForm.calculateMonth) {
        this.msgError("请选择计算月份");
        return;
      }
      
      this.calculateLoading = true;
      const [year, month] = this.calculateForm.calculateMonth.split('-');
      const params = {
        statYear: parseInt(year),
        statMonth: parseInt(month),
        calculateType: this.calculateForm.calculateType,
        teacherId: this.calculateForm.teacherId,
        confirmedOnly: this.calculateForm.confirmedOnly
      };
      
      calculateTeacherCourseFee(params).then(response => {
        this.msgSuccess(`成功计算 ${response.data.processedCount} 位教师的课时费`);
        this.calculateDialogVisible = false;
        this.calculateLoading = false;
        this.getList();
      }).catch(() => {
        this.calculateLoading = false;
      });
    },
    /** 重新计算单个教师 */
    handleRecalculate(row) {
      if (!this.queryParams.statYear || !this.queryParams.statMonth) {
        this.msgError("请先选择统计月份");
        return;
      }
      
      const params = {
        statYear: this.queryParams.statYear,
        statMonth: this.queryParams.statMonth,
        calculateType: 'single',
        teacherId: row.teacherId,
        confirmedOnly: true
      };
      
      this.$confirm(`确认重新计算教师"${row.teacherName}"的课时费吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return calculateTeacherCourseFee(params);
      }).then(() => {
        this.msgSuccess("重新计算成功");
        this.getList();
      }).catch(() => {});
    },
    /** 查看详细记录 */
    handleViewDetail(row) {
      if (!this.queryParams.statYear || !this.queryParams.statMonth) {
        this.msgError("请先选择统计月份");
        return;
      }
      
      this.currentTeacher = row;
      const params = {
        statYear: this.queryParams.statYear,
        statMonth: this.queryParams.statMonth
      };
      
      getTeacherAttendanceDetails(row.teacherId, params).then(response => {
        this.attendanceDetails = response.data;
        this.detailDialogVisible = true;
      });
    },
    /** 导出统计 */
    handleExport() {
      this.download('kg/course/teacher-stats/export', {
        ...this.queryParams
      }, `teacher_course_stats_${new Date().getTime()}.xlsx`);
    },
    /** 计算表格汇总 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (column.property === 'totalSessions') {
          const values = data.map(item => Number(item[column.property]));
          sums[index] = values.reduce((prev, curr) => prev + curr, 0) + '节';
        } else if (column.property === 'totalCourseFee') {
          const values = data.map(item => Number(item[column.property]));
          sums[index] = '￥' + values.reduce((prev, curr) => prev + curr, 0).toFixed(2);
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    /** 加载教师选项 */
    loadTeacherOptions() {
      listAllTeacher().then(response => {
        this.teacherOptions = response.data || [];
      });
    },
    /** 加载课程选项 */
    loadCourseOptions() {
      listAllCourse().then(response => {
        this.courseOptions = response.data || [];
      });
    }
  },
  mounted() {
    // 默认设置为当前月份
    this.queryDate = new Date().toISOString().slice(0, 7);
    this.handleDateChange(this.queryDate);
    this.getList();
  }
};
</script>

<style scoped>
.text-price {
  color: #E6A23C;
  font-weight: bold;
}

.stat-item {
  text-align: center;
  position: relative;
}

.stat-icon {
  font-size: 32px;
  margin-bottom: 12px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.teacher,
.stat-icon.sessions,
.stat-icon.fee,
.stat-icon.average {
  background: linear-gradient(135deg, #409EFF 0%, #667eea 100%);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin: 8px 0;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.course-details {
  max-height: 120px;
  overflow-y: auto;
}

.course-detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

.course-tag {
  margin-right: 8px;
  min-width: 60px;
}

.course-info {
  font-size: 13px;
  color: #666;
}

.course-subtotal {
  color: #E6A23C;
  font-weight: bold;
}

.no-data {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px 0;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
