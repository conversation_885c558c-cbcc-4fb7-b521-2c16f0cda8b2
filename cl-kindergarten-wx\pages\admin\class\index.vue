<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">班级管理</text>
				</view>
			</view>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-section">
			<view class="action-buttons">
				<view class="action-btn primary" @click="addClass">
					<view class="btn-icon">
						<u-icon name="plus" color="#ffffff" size="20"></u-icon>
					</view>
					<text class="btn-text">新增班级</text>
				</view>
				<view class="action-btn secondary" @click="syncClassData">
					<view class="btn-icon">
						<u-icon name="reload" color="#667eea" size="20"></u-icon>
					</view>
					<text class="btn-text">同步班级</text>
				</view>
			</view>
		</view>

		<!-- 班级列表 -->
		<view class="class-list">
			<!-- 加载状态 -->
			<view v-if="loading && classList.length === 0" class="loading-container">
				<u-loading-icon mode="spinner" color="#667eea" size="40"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 空状态 -->
			<view v-else-if="!loading && classList.length === 0" class="empty-container">
				<view class="empty-icon">📚</view>
				<text class="empty-text">暂无班级数据</text>
				<text class="empty-tip">点击上方按钮添加班级</text>
			</view>

			<!-- 班级列表 -->
			<view v-else>
				<view v-for="classItem in classList" :key="classItem.classId" class="class-item-card">
					<view class="class-header">
						<view class="class-icon">{{ getClassIcon(classItem.classType) }}</view>
						<view class="class-basic-info">
							<text class="class-name">{{ classItem.className }}</text>
							<view class="class-meta">
								<text class="class-code">{{ classItem.classCode || classItem.classId }}</text>
								<text class="age-range">{{ classItem.classType || '未分类' }}</text>
							</view>
						</view>
						<view class="capacity-indicator">
							<view class="capacity-circle" :style="{ background: getCapacityColor(classItem) }">
								<text class="capacity-text">{{ getDisplayCount(classItem) }}</text>
							</view>
						</view>
					</view>

					<view class="class-details">
						<view class="detail-row">
							<view class="detail-item">
								<text class="detail-label">👨‍🏫 班主任</text>
								<text class="detail-value">{{ getTeacherName(classItem.headTeacherId) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">👩‍🏫 副班主任</text>
								<text class="detail-value">{{ getTeacherName(classItem.assistantTeacherId) }}</text>
							</view>
						</view>
						<view class="detail-row">
							<view class="detail-item">
								<text class="detail-label">📊 使用率</text>
								<text class="detail-value">{{ getUsageRate(classItem) }}</text>
							</view>
						</view>
						<view class="detail-row">
							<view class="detail-item">
								<text class="detail-label">📅 状态</text>
								<text class="detail-value" :class="classItem.status === '0' ? 'status-active' : 'status-inactive'">
									{{ classItem.status === '0' ? '正常' : '停课' }}
								</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">📝 备注</text>
								<text class="detail-value">{{ classItem.remark || '无' }}</text>
							</view>
						</view>
					</view>

					<view class="class-actions">
						<button class="action-btn primary" @click="viewClassStudents(classItem)">
							<text class="btn-icon">👥</text>
							<text class="btn-text">查看学生</text>
						</button>
						<button class="action-btn secondary" @click="editClass(classItem)">
							<text class="btn-icon">✏️</text>
							<text class="btn-text">编辑班级</text>
						</button>
					</view>
				</view>

				<!-- 加载更多 -->
				<view v-if="hasMore" class="load-more" @click="loadMore">
					<u-loading-icon v-if="loading" mode="spinner" color="#667eea" size="24"></u-loading-icon>
					<text class="load-more-text">{{ loading ? '加载中...' : '点击加载更多' }}</text>
				</view>

				<!-- 没有更多数据 -->
				<view v-else-if="classList.length > 0" class="no-more">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import { getClassList, getTeacherList, getStudentList, syncDingtalkDepartments, syncClassCurrentCount } from '@/api/api.js'

export default {
	data() {
		return {
			classList: [],
			teacherList: [], // 教师列表，用于显示教师姓名
			loading: false,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			hasMore: true,
			searchKeyword: '',
			refreshing: false
		}
	},

	async onLoad() {
		await this.loadTeacherList()
		await this.loadClassList()
	},

	onShow() {
		// 页面显示时刷新数据
	},

	onPullDownRefresh() {
		this.refreshList()
	},

	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMore()
		}
	},

	computed: {
		totalStudents() {
			return this.classList.reduce((total, classItem) => {
				return total + this.parseNumber(classItem.currentCount)
			}, 0)
		},

		averageCapacity() {
			if (this.classList.length === 0) return 0
			const totalCapacityRate = this.classList.reduce((total, classItem) => {
				const currentCount = this.parseNumber(classItem.currentCount)
				const capacity = this.parseNumber(classItem.capacity) || 1
				return total + (currentCount / capacity)
			}, 0)
			return Math.round((totalCapacityRate / this.classList.length) * 100)
		}
	},

	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 添加班级
		addClass() {
			uni.navigateTo({
				url: '/pages/admin/class/add',
				events: {
					// 监听添加页面返回的刷新事件
					refreshClassList: () => {
						this.refreshList()
					}
				}
			})
		},

		// 编辑班级
		editClass(classItem) {
			uni.navigateTo({
				url: `/pages/admin/class/edit?id=${classItem.classId}`,
				events: {
					// 监听编辑页面返回的刷新事件
					refreshClassList: () => {
						this.refreshList()
					}
				}
			})
		},

		// 查看班级学生
		viewClassStudents(classItem) {
			uni.navigateTo({
				url: `/pages/admin/class/students?classId=${classItem.classId}&className=${classItem.className}`
			})
		},

		// 同步班级数据
		syncClassData() {
			uni.showModal({
				title: '同步班级',
				content: '确定要同步班级数据吗？这将从钉钉获取最新的部门信息并更新班级记录和人数统计。',
				success: (res) => {
					if (res.confirm) {
						this.performClassSync()
					}
				}
			})
		},

		// 执行班级同步操作
		async performClassSync() {
			uni.showLoading({
				title: '同步中...'
			})

			try {
				// 先同步钉钉部门到班级
				const syncRes = await syncDingtalkDepartments()

				if (syncRes.code === 200) {
					// 同步成功后，更新班级人数
					try {
						await syncClassCurrentCount()
					} catch (error) {
						console.warn('同步班级人数失败，但部门同步成功:', error)
					}

					uni.hideLoading()
					toast('同步成功！已更新班级数据')
					// 重新加载班级列表
					this.refreshList()
				} else {
					uni.hideLoading()
					toast(syncRes.msg || '同步失败')
				}
			} catch (error) {
				uni.hideLoading()
				toast('同步失败，请稍后重试')
				console.error('同步班级数据失败:', error)
			}
		},

		// 加载教师列表
		async loadTeacherList() {
			try {
				const params = {
					status: '0', // 只获取在职教师
					pageNum: 1,
					pageSize: 100
				}
				const res = await getTeacherList(params)
				if (res.code === 200) {
					this.teacherList = res.rows || []
				}
			} catch (error) {
				console.error('加载教师列表失败:', error)
			}
		},

		// 加载班级列表
		async loadClassList() {
			if (this.loading) return

			this.loading = true
			try {
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}

				// 添加搜索条件
				if (this.searchKeyword.trim()) {
					params.className = this.searchKeyword.trim()
				}

				const res = await getClassList(params)

				if (res.code === 200) {
					const newList = res.rows || []

					// 调试：打印返回的数据结构
					console.log('班级列表数据:', newList)
					if (newList.length > 0) {
						console.log('第一个班级数据:', newList[0])
						console.log('currentCount:', newList[0].currentCount, 'capacity:', newList[0].capacity)
					}

					// 处理数据类型，确保数字字段正确
					const processedList = this.processClassListData(newList)

					// 为每个班级获取实际学生人数（如果需要）
					await this.updateClassStudentCounts(processedList)

					if (this.pageNum === 1) {
						this.classList = processedList
					} else {
						this.classList = [...this.classList, ...processedList]
					}

					this.total = res.total || 0
					this.hasMore = this.classList.length < this.total
				} else {
					toast(res.msg || '加载失败')
				}
			} catch (error) {
				console.error('加载班级列表失败:', error)
				toast('加载失败，请稍后重试')
			} finally {
				this.loading = false
				if (this.refreshing) {
					uni.stopPullDownRefresh()
					this.refreshing = false
				}
			}
		},

		// 刷新列表
		refreshList() {
			this.refreshing = true
			this.pageNum = 1
			this.hasMore = true
			this.loadClassList()
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.pageNum++
				this.loadClassList()
			}
		},

		// 根据教师ID获取教师姓名
		getTeacherName(teacherId) {
			if (!teacherId || !this.teacherList) {
				return '-'
			}
			const teacher = this.teacherList.find(item => item.teacherId === teacherId)
			return teacher ? teacher.teacherName : '-'
		},

		// 根据班级类型获取图标
		getClassIcon(classType) {
			const iconMap = {
				'小班': '🎈',
				'中班': '🎨',
				'大班': '🎓',
				'托管班': '🌟',
				'学前班': '📚',
				'混龄班': '🌈'
			}
			return iconMap[classType] || '🏫'
		},

		// 处理班级列表数据，确保数据类型正确
		processClassListData(classList) {
			return classList.map(classItem => {
				// 确保数字字段是数字类型
				const processedItem = {
					...classItem,
					currentCount: this.parseNumber(classItem.currentCount),
					capacity: this.parseNumber(classItem.capacity) || 30, // 默认容量30
					classId: this.parseNumber(classItem.classId),
					headTeacherId: this.parseNumber(classItem.headTeacherId),
					assistantTeacherId: this.parseNumber(classItem.assistantTeacherId)
				}

				console.log(`处理班级 ${processedItem.className}: currentCount=${processedItem.currentCount}, capacity=${processedItem.capacity}`)
				return processedItem
			})
		},

		// 安全地解析数字
		parseNumber(value) {
			if (value === null || value === undefined || value === '') {
				return 0
			}
			const num = parseInt(value)
			return isNaN(num) ? 0 : num
		},

		// 更新班级学生人数（暂时禁用，等后端修复数据）
		async updateClassStudentCounts(classList) {
			// 暂时注释掉实时获取学生人数的逻辑，避免过多API调用
			// 等后端修复 currentCount 字段的计算逻辑后再启用

			console.log('班级学生人数更新已暂时禁用，等待后端修复数据')

			// 临时解决方案：如果 currentCount 为空，设置一个默认值用于显示
			classList.forEach(classItem => {
				if (classItem.currentCount === null || classItem.currentCount === undefined) {
					// 可以设置为0或者一个占位符
					classItem.currentCount = 0
				}
			})

			return

			// 以下是完整的实时获取逻辑，后续可以启用
			/*
			const needUpdate = classList.some(item =>
				item.currentCount === null ||
				item.currentCount === undefined ||
				item.currentCount === 0
			)

			if (!needUpdate) {
				console.log('班级人数数据完整，无需重新获取')
				return
			}

			console.log('检测到班级人数数据不完整，开始获取实际人数...')

			try {
				const updatePromises = classList
					.filter(item =>
						item.currentCount === null ||
						item.currentCount === undefined ||
						item.currentCount === 0
					)
					.map(async (classItem) => {
						try {
							const res = await getStudentList({
								classId: classItem.classId,
								pageNum: 1,
								pageSize: 1,
								status: '0'
							})

							if (res.code === 200) {
								classItem.currentCount = res.total || 0
								console.log(`班级 ${classItem.className} 实际学生人数: ${classItem.currentCount}`)
							}
						} catch (error) {
							console.error(`获取班级 ${classItem.className} 学生人数失败:`, error)
							classItem.currentCount = 0
						}
					})

				await Promise.all(updatePromises)
			} catch (error) {
				console.error('批量更新班级学生人数失败:', error)
			}
			*/
		},

		// 获取显示的人数文本
		getDisplayCount(classItem) {
			const currentCount = this.parseNumber(classItem.currentCount)
			const capacity = this.parseNumber(classItem.capacity) || 30

			// 如果当前人数和容量都有值，正常显示
			if (capacity > 0) {
				return `${currentCount}/${capacity}`
			}

			// 如果数据不完整，显示占位符
			return '--/--'
		},

		// 获取使用率文本
		getUsageRate(classItem) {
			const currentCount = this.parseNumber(classItem.currentCount)
			const capacity = this.parseNumber(classItem.capacity)

			if (capacity > 0) {
				const rate = Math.round((currentCount / capacity) * 100)
				return `${rate}%`
			}

			return '--'
		},

		// 根据容量使用率获取颜色
		getCapacityColor(classItem) {
			const currentCount = this.parseNumber(classItem.currentCount)
			const capacity = this.parseNumber(classItem.capacity) || 1
			const rate = currentCount / capacity
			if (rate >= 0.9) return 'linear-gradient(135deg, #ff6b6b, #ee5a52)' // 红色 - 接近满员
			if (rate >= 0.7) return 'linear-gradient(135deg, #ffa726, #ff9800)' // 橙色 - 较满
			if (rate >= 0.5) return 'linear-gradient(135deg, #66bb6a, #4caf50)' // 绿色 - 适中
			return 'linear-gradient(135deg, #42a5f5, #2196f3)' // 蓝色 - 较空
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 统计卡片 */
.stats-section {
	margin: 30rpx;
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
	margin: 30rpx;
}

.action-buttons {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	border-radius: 20rpx;
	padding: 24rpx 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&:active {
		transform: translateY(-2rpx);
	}

	&.primary {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 8rpx 30rpx rgba(40, 167, 69, 0.3);

		&:active {
			box-shadow: 0 12rpx 40rpx rgba(40, 167, 69, 0.4);
		}

		.btn-text {
			color: #ffffff;
		}
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #667eea;
		box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.15);

		&:active {
			box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.25);
			background: #f8f9ff;
		}

		.btn-text {
			color: #667eea;
		}
	}
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;

	.primary & {
		background: rgba(255, 255, 255, 0.2);
	}

	.secondary & {
		background: rgba(102, 126, 234, 0.1);
	}
}

.btn-text {
	font-size: 28rpx;
	font-weight: 600;
}

/* 班级列表 */
.class-list {
	padding: 0 30rpx 40rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.loading-text {
	margin-top: 20rpx;
	color: #667eea;
	font-size: 28rpx;
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	color: #666;
	font-size: 32rpx;
	margin-bottom: 10rpx;
}

.empty-tip {
	color: #999;
	font-size: 26rpx;
}

/* 状态样式 */
.status-active {
	color: #4caf50 !important;
}

.status-inactive {
	color: #f44336 !important;
}

/* 加载更多 */
.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	margin-top: 20rpx;
	cursor: pointer;
}

.load-more-text {
	margin-left: 10rpx;
	color: #667eea;
	font-size: 28rpx;
}

/* 没有更多数据 */
.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
	margin-top: 20rpx;
}

.no-more-text {
	color: #999;
	font-size: 26rpx;
}

.class-item-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 0;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.class-header {
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.class-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.class-basic-info {
	flex: 1;
}

.class-name {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
}

.class-meta {
	display: flex;
	gap: 16rpx;
}

.class-code {
	background: #e3f2fd;
	color: #1976d2;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.age-range {
	background: #f3e5f5;
	color: #7b1fa2;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.capacity-indicator {
	display: flex;
	align-items: center;
}

.capacity-circle {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.capacity-text {
	color: white;
	font-size: 20rpx;
	font-weight: 700;
	text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.2);
}

.class-details {
	padding: 24rpx;
	background: #fafafa;
}

.detail-row {
	display: flex;
	gap: 20rpx;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.detail-label {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 600;
}

.class-actions {
	padding: 20rpx 24rpx;
	display: flex;
	gap: 16rpx;
	background: white;
}

.action-btn {
	flex: 1;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 16rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s ease;

	&.primary {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
		}
	}

	&.secondary {
		background: #f5f5f5;
		color: #666;
		border: 1rpx solid #e0e0e0;

		&:active {
			background: #eeeeee;
		}
	}
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 22rpx;
	font-weight: 600;
}
</style>
