<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="课程" prop="courseId">
        <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable>
          <el-option
            v-for="course in courseOptions"
            :key="course.courseId"
            :label="course.courseName"
            :value="course.courseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="活跃" value="active" />
          <el-option label="暂停" value="suspended" />
          <el-option label="完成" value="completed" />
          <el-option label="取消" value="cancelled" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增报名</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <el-table v-loading="loading" :data="enrollmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="120" />
      <el-table-column label="班级" align="center" prop="className" width="100" />
      <el-table-column label="课程名称" align="center" prop="courseName" />
      <el-table-column label="课程类型" align="center" width="100">
        <template #default="scope">
          {{ getCourseTypeText(scope.row.courseType) }}
        </template>
      </el-table-column>
      <el-table-column label="报名日期" align="center" prop="enrollmentDate" width="100" />
      <el-table-column label="课时情况" align="center" width="180">
        <template #default="scope">
          <div>
            <el-progress 
              :percentage="getUsagePercentage(scope.row)" 
              :status="getProgressStatus(scope.row)"
              :show-text="false"
            />
            <div class="course-sessions-info">
              <span>总: {{ scope.row.totalSessions }}</span> |
              <span>已用: {{ scope.row.usedSessions }}</span> |
              <span>剩余: {{ scope.row.remainingSessions }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="费用情况" align="center" width="120">
        <template #default="scope">
          <div>
            <div>总: ￥{{ scope.row.totalAmount }}</div>
            <div>已付: ￥{{ scope.row.paidAmount }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" @click="handleAttendance(scope.row)">考勤记录</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改报名对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="enrollmentRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="学生" prop="studentId">
              <el-select v-model="form.studentId" placeholder="请选择学生" filterable @change="handleStudentChange">
                <el-option
                  v-for="student in studentOptions"
                  :key="student.studentId"
                  :label="`${student.studentName}(${student.className || '暂无班级'})`"
                  :value="student.studentId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程" prop="courseId">
              <el-select v-model="form.courseId" placeholder="请选择课程" @change="handleCourseChange">
                <el-option
                  v-for="course in courseOptions"
                  :key="course.courseId"
                  :label="`${course.courseName}(￥${course.pricePerSession}/节)`"
                  :value="course.courseId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 显示选中课程的详细信息 -->
        <el-row v-if="selectedCourse">
          <el-col :span="24">
            <el-form-item label="课程信息">
              <el-descriptions :column="3" size="small" border>
                <el-descriptions-item label="课程名称">{{ selectedCourse.courseName }}</el-descriptions-item>
                <el-descriptions-item label="课程类型">
                  <dict-tag :options="dict.type.kg_course_type" :value="selectedCourse.courseType"/>
                </el-descriptions-item>
                <el-descriptions-item label="单节价格">￥{{ selectedCourse.pricePerSession }}</el-descriptions-item>
                <el-descriptions-item label="课程时长">{{ selectedCourse.duration }}分钟</el-descriptions-item>
                <el-descriptions-item label="授课老师">
                  <el-tag v-if="selectedCourse.defaultTeacherName" type="success">{{ selectedCourse.defaultTeacherName }}</el-tag>
                  <el-tag v-else type="warning">未设置</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="班级规模">
                  <span v-if="selectedCourse.minStudents && selectedCourse.maxStudents">
                    {{ selectedCourse.minStudents }}-{{ selectedCourse.maxStudents }}人
                  </span>
                  <span v-else>未设置</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="报名日期" prop="enrollmentDate">
              <el-date-picker
                v-model="form.enrollmentDate"
                type="datetime"
                placeholder="选择报名日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总课时数" prop="totalSessions">
              <el-input-number v-model="form.totalSessions" :min="1" :max="200" @change="calculateAmount" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="赠送课时" prop="giftSessions">
              <el-input-number v-model="form.giftSessions" :min="0" :max="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总金额" prop="totalAmount">
              <el-input v-model="form.totalAmount" disabled>
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="已付金额" prop="paidAmount">
              <el-input-number v-model="form.paidAmount" :min="0" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="活跃" value="active" />
                <el-option label="暂停" value="suspended" />
                <el-option label="完成" value="completed" />
                <el-option label="取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 考勤记录弹框 -->
    <el-dialog 
      title="考勤记录" 
      :visible.sync="attendanceDialogVisible" 
      width="800px" 
      append-to-body
    >
      <div v-if="selectedStudent" class="mb10">
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="学生姓名">{{ selectedStudent.studentName }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ selectedStudent.className || '-' }}</el-descriptions-item>
          <el-descriptions-item label="课程名称">{{ selectedStudent.courseName }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <el-table v-loading="attendanceLoading" :data="attendanceList" stripe>
        <el-table-column label="上课日期" prop="attendanceDate" width="120" align="center" />
        <!-- <el-table-column label="开始时间" prop="startTime" width="100" align="center" /> -->
        <!-- <el-table-column label="结束时间" prop="endTime" width="100" align="center" /> -->
        <el-table-column label="考勤状态" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getAttendanceStatusType(scope.row.attendanceStatus)"
              size="small"
            >
              {{ getAttendanceStatusText(scope.row.attendanceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="签到方式" width="100" align="center">
          <template #default="scope">
            <span v-if="scope.row.checkInMethod === 'face'">😊 人脸</span>
            <span v-else-if="scope.row.checkInMethod === 'manual'">✋ 手动</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="确认状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isConfirmed ? 'success' : 'warning'" size="small">
              {{ scope.row.isConfirmed ? '已确认' : '未确认' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100" align="center" show-overflow-tooltip />
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="attendanceDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listEnrollment as listCourseEnrollment, 
  getEnrollment as getCourseEnrollment, 
  delEnrollment as delCourseEnrollment, 
  addEnrollment as addCourseEnrollment, 
  updateEnrollment as updateCourseEnrollment 
} from "@/api/kg/course/enrollment";
import { listAllCourse } from "@/api/kg/course/manage";
import { listAllStudent } from "@/api/kg/student/info";
import { listCourseAttendance } from "@/api/kg/course/attendance";
import dict from '@/mixin/dict'

export default {
  name: "CourseEnrollment",
  dicts: ['kg_course_type'],
  mixins: [dict],
  data() {
    return {
      enrollmentList: [],
      courseOptions: [],
      studentOptions: [],
      selectedCourse: null,
      selectedStudent: null,
      open: false,
      attendanceDialogVisible: false,
      attendanceList: [],
      attendanceLoading: false,
      loading: true,
      showSearch: true,
      ids: [],
      single: true,
      multiple: true,
      total: 0,
      title: "",
      form: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: null,
        courseId: null,
        status: null
      },
      rules: {
        studentId: [
          { required: true, message: "学生不能为空", trigger: "change" }
        ],
        courseId: [
          { required: true, message: "课程不能为空", trigger: "change" }
        ],
        enrollmentDate: [
          { required: true, message: "报名日期不能为空", trigger: "blur" }
        ],
        totalSessions: [
          { required: true, message: "总课时数不能为空", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    /** 查询报名列表 */
    getList() {
      this.loading = true;
      listCourseEnrollment(this.queryParams).then(response => {
        this.enrollmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        enrollmentId: null,
        studentId: null,
        courseId: null,
        classId: null,
        enrollmentDate: null,
        totalSessions: 1,
        usedSessions: 0,
        remainingSessions: 0,
        giftSessions: 0,
        totalAmount: 0,
        paidAmount: 0,
        status: "active",
        remark: null
      };
      this.selectedCourse = null;
      this.$refs.enrollmentRef && this.$refs.enrollmentRef.resetFields();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryRef && this.$refs.queryRef.resetFields();
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.enrollmentId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加报名记录";
      const now = new Date();
      this.form.enrollmentDate = now.getFullYear() + '-' + 
        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
        String(now.getDate()).padStart(2, '0') + ' ' +
        String(now.getHours()).padStart(2, '0') + ':' +
        String(now.getMinutes()).padStart(2, '0') + ':' +
        String(now.getSeconds()).padStart(2, '0');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const enrollmentId = row.enrollmentId || this.ids[0];
      getCourseEnrollment(enrollmentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改报名记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["enrollmentRef"].validate(valid => {
        if (valid) {
          // 计算剩余课时
          this.form.remainingSessions = this.form.totalSessions - (this.form.usedSessions || 0);
          
          if (this.form.enrollmentId != null) {
            updateCourseEnrollment(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改失败:', error);
              this.msgError("修改失败，请检查数据格式");
            });
          } else {
            addCourseEnrollment(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增失败:', error);
              this.msgError("新增失败，请检查数据格式");
            });
          }
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const enrollmentIds = row.enrollmentId || this.ids;
      this.$confirm('是否确认删除报名记录编号为"' + enrollmentIds + '"的数据项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return delCourseEnrollment(enrollmentIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/course/enrollment/export', {
        ...this.queryParams
      }, `course_enrollment_${new Date().getTime()}.xlsx`);
    },
    /** 考勤记录按钮操作 */
    handleAttendance(row) {
      this.$router.push({
        path: '/kg/course/attendance',
        query: { 
          enrollmentId: row.enrollmentId,
          studentId: row.studentId,
          courseId: row.courseId
        }
      });
    },
    /** 课程变更处理 */
    handleCourseChange(courseId) {
      // 清空选中课程
      this.selectedCourse = null;
      
      if (courseId) {
        // 查找选中的课程信息
        const course = this.courseOptions.find(c => c.courseId === courseId);
        if (course) {
          this.selectedCourse = course;
          // 自动计算金额
          this.calculateAmount();
        }
      }
    },
    /** 学生变更处理 */
    handleStudentChange(studentId) {
      if (studentId) {
        // 查找选中的学生信息
        const student = this.studentOptions.find(s => s.studentId === studentId);
        if (student) {
          // 自动设置班级ID
          this.form.classId = student.classId || null;
        }
      } else {
        this.form.classId = null;
      }
    },
    /** 计算总金额 */
    calculateAmount() {
      if (this.selectedCourse && this.form.totalSessions) {
        this.form.totalAmount = (this.selectedCourse.pricePerSession * this.form.totalSessions).toFixed(2);
      } else {
        this.form.totalAmount = 0;
      }
    },
    /** 获取课时使用百分比 */
    getUsagePercentage(row) {
      if (row.totalSessions === 0) return 0;
      return Math.round((row.usedSessions / row.totalSessions) * 100);
    },
    /** 获取进度条状态 */
    getProgressStatus(row) {
      const percentage = this.getUsagePercentage(row);
      if (percentage >= 90) return 'exception';
      if (percentage >= 70) return 'warning';
      return 'success';
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'suspended': 'warning',
        'completed': 'info',
        'cancelled': 'danger'
      };
      return statusMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'active': '活跃',
        'suspended': '暂停',
        'completed': '完成',
        'cancelled': '取消'
      };
      return statusMap[status] || '未知';
    },
    /** 加载课程选项 */
    loadCourseOptions() {
      listAllCourse().then(response => {
        this.courseOptions = response.data || [];
      });
    },
    /** 加载学生选项 */
    loadStudentOptions() {
      listAllStudent().then(response => {
        this.studentOptions = response.data || [];
      });
    },
    
    /** 获取课程类型中文显示 */
    getCourseTypeText(courseType) {
      // 使用系统字典服务获取课程类型中文显示
      return this.selectDictLabel(this.dict.type.kg_course_type, courseType);
    },
    
    /** 考勤记录按钮点击 */
    handleAttendance(row) {
      // 弹框显示考勤记录
      this.selectedStudent = row;
      this.attendanceDialogVisible = true;
      this.loadAttendanceRecords(row.enrollmentId);
    },
    /** 加载考勤记录 */
    loadAttendanceRecords(enrollmentId) {
      this.attendanceLoading = true;
      const queryParams = {
        enrollmentId: enrollmentId,
        studentId: this.selectedStudent.studentId,
        courseId: this.selectedStudent.courseId
      };
      
      listCourseAttendance(queryParams).then(response => {
        this.attendanceList = response.rows || response.data || [];
        this.attendanceLoading = false;
      }).catch(error => {
        console.error('获取考勤记录失败:', error);
        this.attendanceList = [];
        this.attendanceLoading = false;
        this.msgError('获取考勤记录失败，请重试');
      });
    },
    /** 获取考勤状态类型 */
    getAttendanceStatusType(status) {
      const statusMap = {
        'present': 'success',
        'absent': 'danger',
        'late': 'warning',
        'early': 'info'
      };
      return statusMap[status] || 'info';
    },
    /** 获取考勤状态文本 */
    getAttendanceStatusText(status) {
      const statusMap = {
        'present': '出勤',
        'absent': '缺勤',
        'late': '迟到',
        'early': '早退'
      };
      return statusMap[status] || '未知';
    }
  },
  mounted() {
    this.getList();
    this.loadCourseOptions();
    this.loadStudentOptions();
  }
};
</script>

<style scoped>
.course-sessions-info {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}
</style>
