package com.cl.project.business.controller;

import java.util.List;
import java.util.Map;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgCourseBill;
import com.cl.project.business.service.IKgCourseBillService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 托管费账单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/course-bill")
public class KgCourseBillController extends BaseController
{
    @Autowired
    private IKgCourseBillService kgCourseBillService;

    /**
     * 查询托管费账单列表
     */
    @SaCheckPermission("kg:finance:course:list")
    @GetMapping("/list")
    public TableDataInfo list(KgCourseBill kgCourseBill)
    {
        startPage();
        List<KgCourseBill> list = kgCourseBillService.selectKgCourseBillList(kgCourseBill);
        return getDataTable(list);
    }

    /**
     * 导出托管费账单列表
     */
    @SaCheckPermission("kg:finance:course:view")
    @Log(title = "托管费账单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgCourseBill kgCourseBill)
    {
        List<KgCourseBill> list = kgCourseBillService.selectKgCourseBillList(kgCourseBill);
        ExcelUtil<KgCourseBill> util = new ExcelUtil<KgCourseBill>(KgCourseBill.class);
        return util.exportExcel(list, "bill");
    }

    /**
     * 获取托管费账单详细信息
     */
    @SaCheckPermission("kg:finance:course:view")
    @GetMapping(value = "/{billId}")
    public AjaxResult getInfo(@PathVariable("billId") Long billId)
    {
        return AjaxResult.success(kgCourseBillService.selectKgCourseBillById(billId));
    }

    /**
     * 新增托管费账单
     */
    @SaCheckPermission("kg:finance:course:calculate")
    @Log(title = "托管费账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgCourseBill kgCourseBill)
    {
        return toAjax(kgCourseBillService.insertKgCourseBill(kgCourseBill));
    }

    /**
     * 修改托管费账单
     */
    @SaCheckPermission("kg:finance:course:calculate")
    @Log(title = "托管费账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgCourseBill kgCourseBill)
    {
        return toAjax(kgCourseBillService.updateKgCourseBill(kgCourseBill));
    }

    /**
     * 删除托管费账单
     */
    @SaCheckPermission("kg:finance:course:send")
    @Log(title = "托管费账单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{billIds}")
    public AjaxResult remove(@PathVariable Long[] billIds)
    {
        return toAjax(kgCourseBillService.deleteKgCourseBillByIds(billIds));
    }

    /**
     * 生成月度托管费账单
     */
    @Log(title = "生成月度托管费账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateMonthlyBills")
    @SaCheckPermission("kg:bill:generate")
    public AjaxResult generateMonthlyBills(@RequestBody Map<String, Object> params)
    {
        try {
            Integer billYear = params.get("billYear") != null ? Integer.valueOf(params.get("billYear").toString()) : null;
            Integer billMonth = params.get("billMonth") != null ? Integer.valueOf(params.get("billMonth").toString()) : null;
            Long classId = params.get("classId") != null ? Long.valueOf(params.get("classId").toString()) : null;
            Long studentId = params.get("studentId") != null ? Long.valueOf(params.get("studentId").toString()) : null;
            
            int generatedCount = kgCourseBillService.generateMonthlyBills(billYear, billMonth, classId, studentId);
            return AjaxResult.success("成功生成 " + generatedCount + " 条月度账单");
        } catch (Exception e) {
            return AjaxResult.error("账单生成失败：" + e.getMessage());
        }
    }

    /**
     * 发送托管费账单
     */
    @Log(title = "发送托管费账单", businessType = BusinessType.UPDATE)
    @PostMapping("/send")
    public AjaxResult sendBill(@RequestBody Long[] billIds)
    {
        try {
            int sentCount = kgCourseBillService.sendBills(billIds);
            return AjaxResult.success("成功发送 " + sentCount + " 条账单");
        } catch (Exception e) {
            return AjaxResult.error("账单发送失败：" + e.getMessage());
        }
    }

    /**
     * 标记托管费账单为已支付
     */
    @Log(title = "标记托管费账单为已支付", businessType = BusinessType.UPDATE)
    @PostMapping("/markPaid")
    public AjaxResult markBillPaid(@RequestBody Long[] billIds)
    {
        try {
            int paidCount = kgCourseBillService.markBillsPaid(billIds);
            return AjaxResult.success("成功标记 " + paidCount + " 条账单为已支付");
        } catch (Exception e) {
            return AjaxResult.error("账单状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取托管费账单统计
     */
    @GetMapping("/statistics")
    public AjaxResult getBillStatistics(KgCourseBill params)
    {
        try {
            Map<String, Object> statistics = kgCourseBillService.getBillStatistics(params);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            return AjaxResult.error("统计数据获取失败：" + e.getMessage());
        }
    }
}
