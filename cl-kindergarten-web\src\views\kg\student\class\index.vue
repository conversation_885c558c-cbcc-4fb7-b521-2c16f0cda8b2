<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="班级名称" prop="className">
        <el-input
          v-model="queryParams.className"
          placeholder="请输入班级名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="班主任" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="请输入班主任姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="班级状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择班级状态" clearable size="small">
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['kg:student:class:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['kg:student:class:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['kg:student:class:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="mini"
          @click="handleSyncClass"
          v-hasPermi="['kg:student:class:add']"
          :loading="syncLoading"
        >同步班级</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="classList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="班级编号" align="center" prop="classId" />
      <el-table-column label="班级名称" align="center" prop="className" />
      <el-table-column label="班级类型" align="center" prop="classType" />
      <el-table-column label="班主任" align="center">
        <template slot-scope="scope">
          <span>{{ getTeacherName(scope.row.headTeacherId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当前人数" align="center" prop="currentCount" />
      <el-table-column label="班级容量" align="center" prop="capacity" />
      <el-table-column label="班级状态" align="center" prop="status">
        <template slot-scope="scope">
          <span v-if="scope.row.status === '0'" style="color: green;">正常</span>
          <span v-else-if="scope.row.status === '1'" style="color: red;">停用</span>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['kg:class:detail']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['kg:student:class:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['kg:student:class:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改班级对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="班级名称" prop="className">
          <el-input v-model="form.className" placeholder="请输入班级名称" />
        </el-form-item>
        <el-form-item label="班级类型" prop="classType">
          <el-select v-model="form.classType" placeholder="请选择班级类型">
            <el-option label="托班" value="托班" />
            <el-option label="小班" value="小班" />
            <el-option label="中班" value="中班" />
            <el-option label="大班" value="大班" />
          </el-select>
        </el-form-item>
        <el-form-item label="班主任" prop="headTeacherId">
          <el-select v-model="form.headTeacherId" placeholder="请选择班主任" filterable>
            <el-option
              v-for="item in teacherList"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="副班主任" prop="assistantTeacherId">
          <el-select v-model="form.assistantTeacherId" placeholder="请选择副班主任" filterable clearable>
            <el-option
              v-for="item in teacherList"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="班级容量" prop="capacity">
          <el-input-number v-model="form.capacity" :min="1" :max="100" placeholder="请输入班级容量" />
        </el-form-item>
        <el-form-item label="班级状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listClass, getClass, delClass, addClass, updateClass, exportClass, syncClassCurrentCount } from "@/api/kg/student/class";
import { listTeacher } from "@/api/kg/teacher/info";
import { syncDingtalkDepartments } from "@/api/business/dingtalk";

export default {
  name: "ClassManage",
  // dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 同步按钮loading状态
      syncLoading: false,
      // 同步班级人数loading状态
      syncCountLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 班级表格数据
      classList: [],
      // 教师列表
      teacherList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        className: undefined,
        teacherName: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        className: [
          { required: true, message: "班级名称不能为空", trigger: "blur" }
        ],
        headTeacherId: [
          { required: true, message: "班主任不能为空", trigger: "change" }
        ],
        capacity: [
          { required: true, message: "班级容量不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTeacherList();
  },
  methods: {
    /** 查询班级列表 */
    getList() {
      this.loading = true;
      listClass(this.queryParams).then(response => {
        this.classList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询教师列表 */
    getTeacherList() {
      listTeacher().then(response => {
        this.teacherList = response.rows;
      });
    },
    /** 根据教师ID获取教师姓名 */
    getTeacherName(teacherId) {
      if (!teacherId || !this.teacherList) {
        return '-';
      }
      const teacher = this.teacherList.find(item => item.teacherId === teacherId);
      return teacher ? teacher.teacherName : '-';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        classId: undefined,
        className: undefined,
        classType: undefined,
        capacity: undefined,
        currentCount: 0,
        headTeacherId: undefined,
        assistantTeacherId: undefined,
        dingtalkDeptId: undefined,
        status: "0",
        comId: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.classId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加班级";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const classId = row.classId || this.ids
      getClass(classId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改班级";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.classId != undefined) {
            updateClass(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addClass(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 查看详情按钮操作 */
    handleDetail(row) {
      const classId = row.classId;
      this.$router.push({
        path: '/kg/student/class/detail',
        query: { classId: classId }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const classIds = row.classId || this.ids;
      this.$confirm('是否确认删除班级编号为"' + classIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delClass(classIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有班级数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportClass(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    },
    /** 同步班级操作 */
    handleSyncClass() {
      this.$confirm('是否确认同步班级数据?此操作将从钉钉获取部门信息并自动创建或更新班级记录，同时更新班级人数统计。', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "info"
        }).then(() => {
          this.syncLoading = true;
          // 先同步钉钉部门
          return syncDingtalkDepartments();
        }).then(response => {
          if (response.code === 200) {
            // 钉钉同步成功后，再同步班级人数
            return syncClassCurrentCount();
          } else {
            throw new Error(response.msg || '钉钉部门同步失败');
          }
        }).then(response => {
          this.syncLoading = false;
          if (response.code === 200) {
            this.$message.success('同步成功！已更新班级数据和人数统计');
            this.getList(); // 刷新列表
          } else {
            this.$message.warning('钉钉部门同步成功，但班级人数同步失败：' + (response.msg || '未知错误'));
            this.getList(); // 仍然刷新列表
          }
        }).catch(error => {
          this.syncLoading = false;
          if (error.response && error.response.data) {
            this.$message.error('同步失败：' + error.response.data.msg);
          } else if (error.message) {
            this.$message.error('同步失败：' + error.message);
          } else {
            this.$message.error('同步取消或网络异常');
          }
        });
    }
  }
};
</script>
