import request from '@/utils/request'

// 查询托管费账单列表
export function listCourseBill(query) {
  return request({
    url: '/business/course-bill/list',
    method: 'get',
    params: query
  })
}

// 查询托管费账单详细
export function getCourseBill(billId) {
  return request({
    url: '/business/course-bill/' + billId,
    method: 'get'
  })
}

// 新增托管费账单
export function addCourseBill(data) {
  return request({
    url: '/business/course-bill',
    method: 'post',
    data: data
  })
}

// 修改托管费账单
export function updateCourseBill(data) {
  return request({
    url: '/business/course-bill',
    method: 'put',
    data: data
  })
}

// 删除托管费账单
export function delCourseBill(billIds) {
  return request({
    url: '/business/course-bill/' + billIds,
    method: 'delete'
  })
}

// 导出托管费账单
export function exportCourseBill(query) {
  return request({
    url: '/business/course-bill/export',
    method: 'get',
    params: query
  })
}

// 生成月度托管费账单
export function generateMonthlyBills(data) {
  return request({
    url: '/business/course-bill/generateMonthlyBills',
    method: 'post',
    data: data
  })
}

// 发送托管费账单
export function sendBill(billIds) {
  return request({
    url: '/business/course-bill/send',
    method: 'post',
    data: billIds
  })
}

// 标记托管费账单为已支付
export function markBillPaid(billIds) {
  return request({
    url: '/business/course-bill/markPaid',
    method: 'post',
    data: billIds
  })
}

// 获取托管费账单统计
export function getBillStatistics(query) {
  return request({
    url: '/business/course-bill/statistics',
    method: 'get',
    params: query
  })
}

// 批量生成托管费账单
export function batchGenerateCourseBill(data) {
  return request({
    url: '/business/course-bill/batchGenerate',
    method: 'post',
    data: data
  })
}
