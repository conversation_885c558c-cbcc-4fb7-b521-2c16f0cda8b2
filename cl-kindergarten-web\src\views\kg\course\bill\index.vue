<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账单年月" prop="billMonth">
        <el-date-picker
          v-model="queryDate"
          type="month"
          placeholder="选择年月"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleDateChange"
        />
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账单状态" prop="billStatus">
        <el-select v-model="queryParams.billStatus" placeholder="请选择状态" clearable>
          <el-option label="已生成" value="generated" />
          <el-option label="已发送" value="sent" />
          <el-option label="已支付" value="paid" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Plus"
          @click="handleGenerateBills"
        >生成月度账单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Message"
          :disabled="multiple"
          @click="handleBatchSend"
        >批量发送</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Money"
          :disabled="multiple"
          @click="handleBatchMarkPaid"
        >批量标记已付</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <RightToolbar :showSearch.sync="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalBills }}</div>
            <div class="stat-label">总账单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">￥{{ statistics.totalAmount }}</div>
            <div class="stat-label">总金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ statistics.paidBills }}</div>
            <div class="stat-label">已支付</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">￥{{ statistics.paidAmount }}</div>
            <div class="stat-label">已收金额</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="billList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账单编号" align="center" prop="billId" width="100" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="120" />
      <el-table-column label="班级" align="center" prop="className" width="100" />
      <el-table-column label="账单年月" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.billYear }}-{{ String(scope.row.billMonth).padStart(2, '0') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="总金额" align="center" width="100">
        <template #default="scope">
          <span class="text-price">￥{{ scope.row.totalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="赠送课时" align="center" prop="giftSessions" width="100" />
      <el-table-column label="赠送课程" align="center" prop="giftCourseName" width="120" />
      <el-table-column label="账单状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="getBillStatusType(scope.row.billStatus)">
            {{ getBillStatusText(scope.row.billStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发送时间" align="center" prop="sentTime" width="140" />
      <el-table-column label="支付时间" align="center" prop="paidTime" width="140" />
      <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewDetail(scope.row)">查看详情</el-button>
          <el-button 
            v-if="scope.row.billStatus === 'generated'" 
            link 
            type="success" 
            @click="handleSend(scope.row)"
          >发送</el-button>
          <el-button 
            v-if="scope.row.billStatus === 'sent'" 
            link 
            type="warning" 
            @click="handleMarkPaid(scope.row)"
          >标记已付</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 生成账单对话框 -->
    <el-dialog title="生成月度账单" v-model="generateDialogVisible" width="500px">
      <el-form ref="generateRef" :model="generateForm" label-width="100px">
        <el-form-item label="账单年月" required>
          <el-date-picker
            v-model="generateForm.billMonth"
            type="month"
            placeholder="选择年月"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item label="生成范围">
          <el-radio-group v-model="generateForm.generateType">
            <el-radio label="all">所有学生</el-radio>
            <el-radio label="class">指定班级</el-radio>
            <el-radio label="student">指定学生</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="generateForm.generateType === 'class'" label="选择班级">
          <el-select v-model="generateForm.classId" placeholder="请选择班级">
            <el-option
              v-for="cls in classOptions"
              :key="cls.classId"
              :label="cls.className"
              :value="cls.classId"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="generateForm.generateType === 'student'" label="选择学生">
          <el-select v-model="generateForm.studentId" placeholder="请选择学生" filterable>
            <el-option
              v-for="student in studentOptions"
              :key="student.studentId"
              :label="student.studentName"
              :value="student.studentId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmGenerate" :loading="generateLoading">确 定</el-button>
          <el-button @click="generateDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 账单详情对话框 -->
    <el-dialog title="账单详情" v-model="detailDialogVisible" width="800px">
      <div v-if="billDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="账单编号">{{ billDetail.billId }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ billDetail.studentName }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ billDetail.className }}</el-descriptions-item>
          <el-descriptions-item label="账单年月">
            {{ billDetail.billYear }}-{{ String(billDetail.billMonth).padStart(2, '0') }}
          </el-descriptions-item>
          <el-descriptions-item label="总金额">
            <span class="text-price">￥{{ billDetail.totalAmount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="账单状态">
            <el-tag :type="getBillStatusType(billDetail.billStatus)">
              {{ getBillStatusText(billDetail.billStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="赠送课时">{{ billDetail.giftSessions }}节</el-descriptions-item>
          <el-descriptions-item label="赠送课程">{{ billDetail.giftCourseName || '无' }}</el-descriptions-item>
          <el-descriptions-item label="发送时间">{{ billDetail.sentTime || '未发送' }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ billDetail.paidTime || '未支付' }}</el-descriptions-item>
        </el-descriptions>

        <el-divider>账单明细</el-divider>
        <el-table :data="billDetail.details" border>
          <el-table-column label="课程名称" prop="courseName" />
          <el-table-column label="课时数" prop="sessionsCount" align="center" width="100" />
          <el-table-column label="单价" align="center" width="100">
            <template #default="scope">
              <span>￥{{ scope.row.pricePerSession }}</span>
            </template>
          </el-table-column>
          <el-table-column label="小计" align="center" width="120">
            <template #default="scope">
              <span class="text-price">￥{{ scope.row.totalAmount }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script name="CourseBill">
import { 
  listCourseBill, 
  getCourseBill, 
  delCourseBill,
  generateMonthlyBills,
  sendBill,
  markBillPaid,
  getBillStatistics
} from "@/api/kg/course/bill";
import { listAllClass } from "@/api/kg/class/manage";
import { listAllStudent } from "@/api/kg/student/info";

export default {
  name: "CourseBill",
  data() {
    return {
      billList: [],
      classOptions: [],
      studentOptions: [],
      loading: true,
      showSearch: true,
      ids: [],
      multiple: true,
      total: 0,
      queryDate: '',
      generateDialogVisible: false,
      detailDialogVisible: false,
      generateLoading: false,
      billDetail: null,
      // 统计数据
      statistics: {
        totalBills: 0,
        totalAmount: 0,
        paidBills: 0,
        paidAmount: 0
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: null,
        billYear: null,
        billMonth: null,
        billStatus: null
      },
      generateForm: {
        billMonth: '',
        generateType: 'all',
        classId: null,
        studentId: null
      }
    };
  },
  methods: {
    /** 查询账单列表 */
    getList() {
      this.loading = true;
      listCourseBill(this.queryParams).then(response => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      
      // 获取统计信息
      getBillStatistics(this.queryParams).then(response => {
        this.statistics = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryRef && this.$refs.queryRef.resetFields();
      this.queryDate = '';
      this.queryParams.billYear = null;
      this.queryParams.billMonth = null;
      this.handleQuery();
    },
    /** 日期变化处理 */
    handleDateChange(value) {
      if (value) {
        const [year, month] = value.split('-');
        this.queryParams.billYear = parseInt(year);
        this.queryParams.billMonth = parseInt(month);
      } else {
        this.queryParams.billYear = null;
        this.queryParams.billMonth = null;
      }
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.billId);
      this.multiple = !selection.length;
    },
    /** 生成账单按钮操作 */
    handleGenerateBills() {
      this.generateForm = {
        billMonth: '',
        generateType: 'all',
        classId: null,
        studentId: null
      };
      this.generateDialogVisible = true;
    },
    /** 确认生成账单 */
    confirmGenerate() {
      if (!this.generateForm.billMonth) {
        this.msgError("请选择账单年月");
        return;
      }
      
      this.generateLoading = true;
      const [year, month] = this.generateForm.billMonth.split('-');
      const params = {
        billYear: parseInt(year),
        billMonth: parseInt(month),
        generateType: this.generateForm.generateType,
        classId: this.generateForm.classId,
        studentId: this.generateForm.studentId
      };
      
      generateMonthlyBills(params).then(response => {
        this.msgSuccess(`成功生成 ${response.data} 条账单记录`);
        this.generateDialogVisible = false;
        this.generateLoading = false;
        this.getList();
      }).catch(() => {
        this.generateLoading = false;
      });
    },
    /** 发送账单 */
    handleSend(row) {
      this.$confirm(`确认发送学生“${row.studentName}”的账单吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return sendBill(row.billId);
      }).then(() => {
        this.getList();
        this.msgSuccess("发送成功");
      }).catch(() => {});
    },
    /** 批量发送 */
    handleBatchSend() {
      const billIds = this.ids;
      this.$confirm(`确认批量发送选中的 ${billIds.length} 条账单吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return Promise.all(billIds.map(id => sendBill(id)));
      }).then(() => {
        this.getList();
        this.msgSuccess("批量发送成功");
      }).catch(() => {});
    },
    /** 标记已付 */
    handleMarkPaid(row) {
      this.$confirm(`确认标记学生“${row.studentName}”的账单为已支付吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return markBillPaid(row.billId);
      }).then(() => {
        this.getList();
        this.msgSuccess("标记成功");
      }).catch(() => {});
    },
    /** 批量标记已付 */
    handleBatchMarkPaid() {
      const billIds = this.ids;
      this.$confirm(`确认批量标记选中的 ${billIds.length} 条账单为已支付吗？`, "确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return Promise.all(billIds.map(id => markBillPaid(id)));
      }).then(() => {
        this.getList();
        this.msgSuccess("批量标记成功");
      }).catch(() => {});
    },
    /** 查看详情 */
    handleViewDetail(row) {
      getCourseBill(row.billId).then(response => {
        this.billDetail = response.data;
        this.detailDialogVisible = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const billIds = row.billId || this.ids;
      this.$confirm('是否确认删除账单编号为"' + billIds + '"的数据项？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return delCourseBill(billIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('kg/course/bill/export', {
        ...this.queryParams
      }, `course_bill_${new Date().getTime()}.xlsx`);
    },
    /** 获取账单状态类型 */
    getBillStatusType(status) {
      const statusMap = {
        'generated': 'info',
        'sent': 'warning', 
        'paid': 'success'
      };
      return statusMap[status] || 'info';
    },
    /** 获取账单状态文本 */
    getBillStatusText(status) {
      const statusMap = {
        'generated': '已生成',
        'sent': '已发送',
        'paid': '已支付'
      };
      return statusMap[status] || '未知';
    },
    /** 加载班级选项 */
    loadClassOptions() {
      listAllClass().then(response => {
        this.classOptions = response.data || [];
      });
    },
    /** 加载学生选项 */
    loadStudentOptions() {
      listAllStudent().then(response => {
        this.studentOptions = response.data || [];
      });
    }
  },
  mounted() {
    this.getList();
    this.loadClassOptions();
    this.loadStudentOptions();
  }
};
</script>

<style scoped>
.text-price {
  color: #E6A23C;
  font-weight: bold;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
