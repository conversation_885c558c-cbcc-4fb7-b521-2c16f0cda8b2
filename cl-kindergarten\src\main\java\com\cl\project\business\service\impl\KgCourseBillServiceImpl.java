package com.cl.project.business.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgCourseBillMapper;
import com.cl.project.business.mapper.KgCourseEnrollmentMapper;
import com.cl.project.business.domain.KgCourseBill;
import com.cl.project.business.domain.KgCourseEnrollment;
import com.cl.project.business.service.IKgCourseBillService;

/**
 * 托管费账单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseBillServiceImpl implements IKgCourseBillService 
{
    @Autowired
    private KgCourseBillMapper kgCourseBillMapper;
    
    @Autowired
    private KgCourseEnrollmentMapper kgCourseEnrollmentMapper;

    /**
     * 查询托管费账单
     * 
     * @param billId 托管费账单ID
     * @return 托管费账单
     */
    @Override
    public KgCourseBill selectKgCourseBillById(Long billId)
    {
        return kgCourseBillMapper.selectKgCourseBillById(billId);
    }

    /**
     * 查询托管费账单列表
     * 
     * @param kgCourseBill 托管费账单
     * @return 托管费账单
     */
    @Override
    public List<KgCourseBill> selectKgCourseBillList(KgCourseBill kgCourseBill)
    {
        return kgCourseBillMapper.selectKgCourseBillList(kgCourseBill);
    }

    /**
     * 新增托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    @Override
    public int insertKgCourseBill(KgCourseBill kgCourseBill)
    {
        kgCourseBill.setCreateTime(DateUtils.getNowDate());
        return kgCourseBillMapper.insertKgCourseBill(kgCourseBill);
    }

    /**
     * 修改托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    @Override
    public int updateKgCourseBill(KgCourseBill kgCourseBill)
    {
        kgCourseBill.setUpdateTime(DateUtils.getNowDate());
        return kgCourseBillMapper.updateKgCourseBill(kgCourseBill);
    }

    /**
     * 批量删除托管费账单
     * 
     * @param billIds 需要删除的托管费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillByIds(Long[] billIds)
    {
        return kgCourseBillMapper.deleteKgCourseBillByIds(billIds);
    }

    /**
     * 删除托管费账单信息
     * 
     * @param billId 托管费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillById(Long billId)
    {
        return kgCourseBillMapper.deleteKgCourseBillById(billId);
    }

    /**
     * 生成月度托管费账单
     */
    @Override
    @Transactional
    public int generateMonthlyBills(Integer billYear, Integer billMonth, Long classId, Long studentId) {
        // 参数校验
        if (billYear == null || billMonth == null) {
            throw new RuntimeException("账单年份和月份不能为空");
        }
        
        // 查询符合条件的报名记录
        KgCourseEnrollment enrollmentQuery = new KgCourseEnrollment();
        if (classId != null) {
            enrollmentQuery.setClassId(classId);
        }
        if (studentId != null) {
            enrollmentQuery.setStudentId(studentId);
        }
        enrollmentQuery.setStatus("active"); // 只生成活跃状态的报名账单
        
        List<KgCourseEnrollment> enrollments = kgCourseEnrollmentMapper.selectKgCourseEnrollmentList(enrollmentQuery);
        
        Long yearLong = billYear.longValue();
        Long monthLong = billMonth.longValue();
        
        int generatedCount = 0;
        for (KgCourseEnrollment enrollment : enrollments) {
            // 检查该月是否已经生成账单
            KgCourseBill existBill = new KgCourseBill();
            existBill.setStudentId(enrollment.getStudentId());
            existBill.setBillYear(yearLong);
            existBill.setBillMonth(monthLong);
            
            List<KgCourseBill> existBills = kgCourseBillMapper.selectKgCourseBillList(existBill);
            if (existBills.isEmpty()) {
                // 创建新账单，基于报名信息计算金额
                KgCourseBill newBill = new KgCourseBill();
                newBill.setStudentId(enrollment.getStudentId());
                newBill.setBillYear(yearLong);
                newBill.setBillMonth(monthLong);
                
                // 根据报名的总金额计算月度费用，如果没有则使用默认值
                BigDecimal monthlyAmount = new BigDecimal("500.00");
                if (enrollment.getTotalAmount() != null && enrollment.getTotalSessions() != null && enrollment.getTotalSessions() > 0) {
                    monthlyAmount = enrollment.getTotalAmount().divide(new BigDecimal(enrollment.getTotalSessions()), 2, RoundingMode.HALF_UP);
                }
                
                newBill.setTotalAmount(monthlyAmount);
                newBill.setBillStatus("unpaid");
                newBill.setCreateTime(new Date());
                
                kgCourseBillMapper.insertKgCourseBill(newBill);
                generatedCount++;
            }
        }
        
        return generatedCount;
    }

    /**
     * 发送托管费账单
     */
    @Override
    @Transactional
    public int sendBills(Long[] billIds) {
        int sentCount = 0;
        for (Long billId : billIds) {
            KgCourseBill bill = kgCourseBillMapper.selectKgCourseBillById(billId);
            if (bill != null && !"sent".equals(bill.getBillStatus()) && !"paid".equals(bill.getBillStatus())) {
                bill.setBillStatus("sent");
                bill.setSentTime(new Date());
                kgCourseBillMapper.updateKgCourseBill(bill);
                sentCount++;
                
                // TODO: 实际发送逻辑，如发送微信消息或短信
            }
        }
        return sentCount;
    }

    /**
     * 标记账单为已支付
     */
    @Override
    @Transactional
    public int markBillsPaid(Long[] billIds) {
        int paidCount = 0;
        for (Long billId : billIds) {
            KgCourseBill bill = kgCourseBillMapper.selectKgCourseBillById(billId);
            if (bill != null && !"paid".equals(bill.getBillStatus())) {
                bill.setBillStatus("paid");
                bill.setPaidTime(new Date());
                kgCourseBillMapper.updateKgCourseBill(bill);
                paidCount++;
            }
        }
        return paidCount;
    }

    /**
     * 获取账单统计信息
     */
    @Override
    public Map<String, Object> getBillStatistics(KgCourseBill params) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 查询所有账单
        List<KgCourseBill> allBills = kgCourseBillMapper.selectKgCourseBillList(params);
        
        long totalBills = allBills.size();
        long paidBills = allBills.stream().filter(bill -> "paid".equals(bill.getBillStatus())).count();
        long unpaidBills = totalBills - paidBills;
        
        BigDecimal totalAmount = allBills.stream()
            .map(KgCourseBill::getTotalAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
            
        BigDecimal paidAmount = allBills.stream()
            .filter(bill -> "paid".equals(bill.getBillStatus()))
            .map(KgCourseBill::getTotalAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        statistics.put("totalBills", totalBills);
        statistics.put("paidBills", paidBills);
        statistics.put("unpaidBills", unpaidBills);
        statistics.put("totalAmount", totalAmount);
        statistics.put("paidAmount", paidAmount);
        statistics.put("unpaidAmount", totalAmount.subtract(paidAmount));
        
        return statistics;
    }
}
